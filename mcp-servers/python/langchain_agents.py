#!/usr/bin/env python3
"""
LangChain/LangGraph/LangSmith Agent System
Advanced AI agents with RAG graph memory and self-improvement
"""

import asyncio
import json
import sys
import argparse
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import tempfile
import shutil
import git

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from langchain.agents import AgentExecutor, create_openai_functions_agent
    from langchain.tools import Tool
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.schema import Document
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.vectorstores import Chroma
    from langchain.chains import RetrievalQA
    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
    LANGCHAIN_AVAILABLE = True
except ImportError:
    logger.warning("<PERSON><PERSON>hain not available, using mock implementations")
    LANGCHAIN_AVAILABLE = False

try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolExecutor
    from langgraph.checkpoint.sqlite import SqliteSaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    logger.warning("LangGraph not available")
    LANGGRAPH_AVAILABLE = False

try:
    from langsmith import Client as LangSmithClient
    LANGSMITH_AVAILABLE = True
except ImportError:
    logger.warning("LangSmith not available")
    LANGSMITH_AVAILABLE = False

class RAGGraphMemory:
    def __init__(self, persist_directory: str = "./rag_memory"):
        self.persist_directory = persist_directory
        self.embeddings = None
        self.vectorstore = None
        self.knowledge_graph = {}
        self.improvement_history = []
        
        if LANGCHAIN_AVAILABLE:
            self.embeddings = OpenAIEmbeddings()
            self.vectorstore = Chroma(
                persist_directory=persist_directory,
                embedding_function=self.embeddings
            )
    
    def add_documents(self, documents: List[Document], metadata: Dict[str, Any] = None):
        """Add documents to RAG memory with graph connections"""
        if not self.vectorstore:
            return {"success": False, "error": "Vector store not available"}
        
        try:
            # Add to vector store
            self.vectorstore.add_documents(documents)
            
            # Build knowledge graph connections
            for doc in documents:
                doc_id = doc.metadata.get('id', str(hash(doc.page_content)))
                self.knowledge_graph[doc_id] = {
                    'content': doc.page_content[:200],
                    'metadata': doc.metadata,
                    'connections': [],
                    'improvement_score': 1.0
                }
            
            return {"success": True, "documents_added": len(documents)}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def similarity_search(self, query: str, k: int = 5, threshold: float = 0.7):
        """Search with similarity threshold and graph enhancement"""
        if not self.vectorstore:
            return {"success": False, "error": "Vector store not available"}
        
        try:
            results = self.vectorstore.similarity_search_with_score(query, k=k)
            
            # Filter by threshold and enhance with graph connections
            filtered_results = []
            for doc, score in results:
                if score >= threshold:
                    doc_id = doc.metadata.get('id', str(hash(doc.page_content)))
                    graph_data = self.knowledge_graph.get(doc_id, {})
                    
                    filtered_results.append({
                        'content': doc.page_content,
                        'metadata': doc.metadata,
                        'similarity_score': score,
                        'graph_connections': graph_data.get('connections', []),
                        'improvement_score': graph_data.get('improvement_score', 1.0)
                    })
            
            return {
                "success": True,
                "results": filtered_results,
                "query": query,
                "total_results": len(filtered_results)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def self_improve(self, feedback: Dict[str, Any]):
        """Self-improvement based on feedback"""
        try:
            improvement_id = len(self.improvement_history)
            
            # Analyze feedback and adjust improvement scores
            if feedback.get('accuracy_score', 0) > 0.85:
                # Positive feedback - boost related documents
                query = feedback.get('query', '')
                if query:
                    results = self.similarity_search(query, k=3, threshold=0.6)
                    for result in results.get('results', []):
                        doc_id = result['metadata'].get('id')
                        if doc_id in self.knowledge_graph:
                            current_score = self.knowledge_graph[doc_id]['improvement_score']
                            self.knowledge_graph[doc_id]['improvement_score'] = min(2.0, current_score * 1.1)
            
            self.improvement_history.append({
                'id': improvement_id,
                'feedback': feedback,
                'timestamp': feedback.get('timestamp'),
                'improvements_made': True
            })
            
            return {
                "success": True,
                "improvement_id": improvement_id,
                "improvements_applied": True
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

class RepositoryIngestor:
    def __init__(self, rag_memory: RAGGraphMemory):
        self.rag_memory = rag_memory
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", " ", ""]
        ) if LANGCHAIN_AVAILABLE else None
    
    def ingest_repository(self, repo_url: str, local_path: Optional[str] = None):
        """Ingest a repository into RAG memory"""
        try:
            # Clone or copy repository
            if repo_url.startswith(('http', 'git')):
                # Clone from URL
                temp_dir = tempfile.mkdtemp()
                repo = git.Repo.clone_from(repo_url, temp_dir)
                repo_path = temp_dir
            else:
                # Local repository path
                repo_path = repo_url
            
            # Process repository files
            documents = []
            supported_extensions = {'.py', '.js', '.ts', '.md', '.txt', '.json', '.yaml', '.yml'}
            
            for file_path in Path(repo_path).rglob('*'):
                if file_path.is_file() and file_path.suffix in supported_extensions:
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        
                        # Split content into chunks
                        if self.text_splitter:
                            chunks = self.text_splitter.split_text(content)
                        else:
                            chunks = [content]
                        
                        for i, chunk in enumerate(chunks):
                            doc = Document(
                                page_content=chunk,
                                metadata={
                                    'id': f"{file_path.name}_{i}",
                                    'file_path': str(file_path.relative_to(repo_path)),
                                    'file_type': file_path.suffix,
                                    'chunk_index': i,
                                    'repo_url': repo_url,
                                    'total_chunks': len(chunks)
                                }
                            )
                            documents.append(doc)
                    
                    except Exception as e:
                        logger.warning(f"Failed to process {file_path}: {e}")
            
            # Add to RAG memory
            result = self.rag_memory.add_documents(documents)
            
            # Cleanup temporary directory
            if repo_url.startswith(('http', 'git')):
                shutil.rmtree(temp_dir)
            
            return {
                "success": True,
                "repo_url": repo_url,
                "documents_processed": len(documents),
                "files_processed": len(set(doc.metadata['file_path'] for doc in documents)),
                "rag_result": result
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

class SelfImprovingAgent:
    def __init__(self, rag_memory: RAGGraphMemory, agent_name: str = "SelfImprovingAgent"):
        self.rag_memory = rag_memory
        self.agent_name = agent_name
        self.llm = None
        self.agent = None
        self.memory = None
        
        if LANGCHAIN_AVAILABLE:
            self.llm = ChatOpenAI(temperature=0, model="gpt-4")
            self.memory = ConversationBufferWindowMemory(
                memory_key="chat_history",
                return_messages=True,
                k=10
            )
            self._create_agent()
    
    def _create_agent(self):
        """Create the self-improving agent with RAG tools"""
        if not LANGCHAIN_AVAILABLE:
            return
        
        # Create RAG tool
        rag_tool = Tool(
            name="rag_search",
            description="Search the knowledge base for relevant information",
            func=self._rag_search
        )
        
        # Create improvement tool
        improvement_tool = Tool(
            name="self_improve",
            description="Provide feedback for self-improvement",
            func=self._self_improve
        )
        
        tools = [rag_tool, improvement_tool]
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a self-improving AI agent with access to a RAG knowledge base.
            
            Your capabilities:
            1. Search the knowledge base for accurate information
            2. Provide factual responses based on retrieved context
            3. Self-improve based on feedback
            4. Avoid hallucination by only using retrieved information
            
            Always use the rag_search tool before answering questions.
            If you cannot find relevant information, say so explicitly.
            After providing an answer, use self_improve to learn from the interaction.
            """),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        self.agent = create_openai_functions_agent(self.llm, tools, prompt)
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True
        )
    
    def _rag_search(self, query: str) -> str:
        """RAG search tool function"""
        result = self.rag_memory.similarity_search(query, k=5, threshold=0.7)
        
        if result["success"] and result["results"]:
            context = "\n\n".join([
                f"Source: {r['metadata'].get('file_path', 'Unknown')}\n{r['content']}"
                for r in result["results"]
            ])
            return f"Retrieved context:\n{context}"
        else:
            return "No relevant information found in the knowledge base."
    
    def _self_improve(self, feedback: str) -> str:
        """Self-improvement tool function"""
        try:
            feedback_data = json.loads(feedback) if feedback.startswith('{') else {"feedback": feedback}
            result = self.rag_memory.self_improve(feedback_data)
            return f"Self-improvement applied: {result}"
        except Exception as e:
            return f"Self-improvement failed: {e}"
    
    def process_query(self, query: str, context: Dict[str, Any] = None):
        """Process a query with the self-improving agent"""
        if not self.agent_executor:
            return {
                "success": False,
                "error": "Agent not available",
                "response": "LangChain not configured"
            }
        
        try:
            response = self.agent_executor.invoke({"input": query})
            
            return {
                "success": True,
                "query": query,
                "response": response.get("output", ""),
                "agent_name": self.agent_name,
                "context_used": context or {}
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

async def main():
    parser = argparse.ArgumentParser(description='LangChain Agent Operations')
    parser.add_argument('operation', choices=[
        'ingest_repo', 'query_agent', 'rag_search', 'self_improve', 'create_agent'
    ])
    parser.add_argument('--repo-url', help='Repository URL or path to ingest')
    parser.add_argument('--query', help='Query for agent or RAG search')
    parser.add_argument('--agent-name', default='SelfImprovingAgent', help='Agent name')
    parser.add_argument('--feedback', help='Feedback for self-improvement')
    parser.add_argument('--threshold', type=float, default=0.7, help='Similarity threshold')
    
    args = parser.parse_args()
    
    # Initialize components
    rag_memory = RAGGraphMemory()
    repo_ingestor = RepositoryIngestor(rag_memory)
    
    try:
        if args.operation == 'ingest_repo':
            if not args.repo_url:
                print(json.dumps({'success': False, 'error': 'Repository URL required'}))
                return
            
            result = repo_ingestor.ingest_repository(args.repo_url)
            print(json.dumps(result))
        
        elif args.operation == 'query_agent':
            if not args.query:
                print(json.dumps({'success': False, 'error': 'Query required'}))
                return
            
            agent = SelfImprovingAgent(rag_memory, args.agent_name)
            result = agent.process_query(args.query)
            print(json.dumps(result))
        
        elif args.operation == 'rag_search':
            if not args.query:
                print(json.dumps({'success': False, 'error': 'Query required'}))
                return
            
            result = rag_memory.similarity_search(args.query, threshold=args.threshold)
            print(json.dumps(result))
        
        elif args.operation == 'self_improve':
            if not args.feedback:
                print(json.dumps({'success': False, 'error': 'Feedback required'}))
                return
            
            feedback_data = json.loads(args.feedback) if args.feedback.startswith('{') else {"feedback": args.feedback}
            result = rag_memory.self_improve(feedback_data)
            print(json.dumps(result))
        
        elif args.operation == 'create_agent':
            agent = SelfImprovingAgent(rag_memory, args.agent_name)
            result = {
                "success": True,
                "agent_name": args.agent_name,
                "capabilities": [
                    "rag_search",
                    "self_improvement",
                    "hallucination_prevention",
                    "context_aware_responses"
                ]
            }
            print(json.dumps(result))
    
    except Exception as e:
        print(json.dumps({'success': False, 'error': str(e)}))

if __name__ == '__main__':
    asyncio.run(main())
