# Lang<PERSON>hain/LangGraph/<PERSON><PERSON>mith Requirements
# Core LangChain
langchain>=0.1.0
langchain-openai>=0.0.5
langchain-community>=0.0.10

# LangGraph for workflow orchestration
langgraph>=0.0.20

# LangSmith for monitoring and optimization
langsmith>=0.0.80

# Vector stores and embeddings
chromadb>=0.4.0
faiss-cpu>=1.7.4

# Document processing
pypdf>=3.17.0
python-docx>=0.8.11
markdown>=3.5.0
beautifulsoup4>=4.12.0

# Repository handling
GitPython>=3.1.40

# Database and storage
sqlite3
psycopg2-binary>=2.9.0
supabase>=2.0.0

# Text processing
tiktoken>=0.5.0
sentence-transformers>=2.2.0

# Async and utilities
aiofiles>=23.0.0
asyncio
pydantic>=2.0.0

# Optional: Advanced features
# neo4j>=5.0.0  # For graph databases
# redis>=5.0.0  # For caching
# elasticsearch>=8.0.0  # For advanced search
