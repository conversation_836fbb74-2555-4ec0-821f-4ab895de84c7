#!/usr/bin/env node

import MCPClient from './client/mcp-client.js';
import chalk from 'chalk';

async function crawlArbCoZa() {
  console.log(chalk.cyan('🕷️ Crawling arb.co.za...'));
  
  const client = new MCPClient();
  
  try {
    // Connect to API server for web scraping
    await client.connectToServer('api');
    await new Promise(r => setTimeout(r, 2000));
    
    console.log(chalk.blue('🌐 Scraping arb.co.za...'));
    
    // First, let's scrape the main page
    const mainPageResult = await client.scrapeWeb('https://arb.co.za', null, 'data');
    
    console.log(chalk.green('✅ Main page scraped successfully!'));
    console.log(chalk.white('📊 Page Title:'), mainPageResult.data.title);
    console.log(chalk.white('📊 Description:'), mainPageResult.data.description);
    console.log(chalk.white('📊 Headings found:'), mainPageResult.data.headings.h1.length + mainPageResult.data.headings.h2.length);
    console.log(chalk.white('📊 Links found:'), mainPageResult.data.links.length);
    console.log(chalk.white('📊 Images found:'), mainPageResult.data.images.length);
    
    // Show main headings
    if (mainPageResult.data.headings.h1.length > 0) {
      console.log(chalk.cyan('\n🏷️ Main Headings (H1):'));
      mainPageResult.data.headings.h1.forEach((heading, index) => {
        console.log(chalk.white(`  ${index + 1}. ${heading}`));
      });
    }
    
    if (mainPageResult.data.headings.h2.length > 0) {
      console.log(chalk.cyan('\n🏷️ Sub Headings (H2):'));
      mainPageResult.data.headings.h2.slice(0, 5).forEach((heading, index) => {
        console.log(chalk.white(`  ${index + 1}. ${heading}`));
      });
      if (mainPageResult.data.headings.h2.length > 5) {
        console.log(chalk.gray(`  ... and ${mainPageResult.data.headings.h2.length - 5} more`));
      }
    }
    
    // Show some key links
    if (mainPageResult.data.links.length > 0) {
      console.log(chalk.cyan('\n🔗 Key Links:'));
      const keyLinks = mainPageResult.data.links
        .filter(link => link.href && link.href.includes('arb.co.za'))
        .slice(0, 10);
      
      keyLinks.forEach((link, index) => {
        console.log(chalk.white(`  ${index + 1}. ${link.text} -> ${link.href}`));
      });
    }
    
    // Try to get specific content sections
    console.log(chalk.blue('\n🔍 Extracting specific content sections...'));
    
    // Look for navigation or menu items
    const navResult = await client.scrapeWeb('https://arb.co.za', 'nav, .menu, .navigation', 'text');
    if (navResult.data && navResult.data.length > 0) {
      console.log(chalk.cyan('\n📋 Navigation Menu:'));
      navResult.data.forEach((item, index) => {
        if (item.trim()) {
          console.log(chalk.white(`  ${index + 1}. ${item.trim()}`));
        }
      });
    }
    
    // Look for main content
    const contentResult = await client.scrapeWeb('https://arb.co.za', 'main, .content, .main-content, article', 'text');
    if (contentResult.data && contentResult.data.length > 0) {
      console.log(chalk.cyan('\n📄 Main Content Preview:'));
      contentResult.data.slice(0, 3).forEach((content, index) => {
        const preview = content.trim().substring(0, 200);
        if (preview) {
          console.log(chalk.white(`  ${index + 1}. ${preview}...`));
        }
      });
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Crawling failed:'), error.message);
  } finally {
    await client.disconnect();
  }
}

crawlArbCoZa();
