#!/usr/bin/env node

/**
 * 🧠 LangChain/LangGraph/<PERSON><PERSON><PERSON> MCP Server
 * 
 * Provides AI assistant with advanced LangChain capabilities:
 * - Repository ingestion and processing
 * - RAG graph memory with self-improvement
 * - LangGraph workflow orchestration
 * - LangSmith monitoring and optimization
 * - Hallucination-free AI agents
 * - Multi-modal document processing
 */

import { WebSocketServer } from 'ws';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class LangChainMCPServer {
  constructor(port = process.env.MCP_LANGCHAIN_PORT || 8775) {
    this.port = port;
    this.wss = null;
    this.clients = new Map();
    this.agentCache = new Map();
    this.ragGraphs = new Map();
    
    // <PERSON><PERSON><PERSON><PERSON> configuration
    this.config = {
      openaiApiKey: process.env.OPENAI_API_KEY,
      langsmithApiKey: process.env.LANGSMITH_API_KEY,
      langsmithProject: process.env.LANGSMITH_PROJECT || 'mcp-langchain-agents',
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseKey: process.env.SUPABASE_ANON_KEY,
      pythonPath: process.env.PYTHON_PATH || 'python3',
      langchainScript: join(__dirname, '../python/langchain_agents.py'),
      maxConcurrentAgents: 5,
      ragMemoryTimeout: 3600000, // 1 hour
      selfImprovementThreshold: 0.85
    };
    
    this.validateConfiguration();
    this.initializeLangChainEnvironment();
  }

  validateConfiguration() {
    const required = ['openaiApiKey'];
    const missing = required.filter(key => !this.config[key]);
    
    if (missing.length > 0) {
      console.warn(chalk.yellow(`⚠️ Missing LangChain configuration: ${missing.join(', ')}`));
      console.warn(chalk.yellow('🔧 Some features may be limited'));
    } else {
      console.log(chalk.green('✅ LangChain configuration validated'));
    }
  }

  async initializeLangChainEnvironment() {
    try {
      // Create Python environment for LangChain agents
      const pythonDir = join(__dirname, '../python');
      await fs.mkdir(pythonDir, { recursive: true });
      
      // Check if LangChain script exists
      try {
        await fs.access(this.config.langchainScript);
        console.log(chalk.green('✅ LangChain script found'));
      } catch {
        console.log(chalk.yellow('⚠️ Creating LangChain agent script...'));
        await this.createLangChainScript();
      }
      
      // Initialize RAG graph memory
      await this.initializeRAGGraphMemory();
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize LangChain environment:'), error.message);
    }
  }

  async createLangChainScript() {
    const pythonScript = `#!/usr/bin/env python3
"""
LangChain/LangGraph/LangSmith Agent System
Advanced AI agents with RAG graph memory and self-improvement
"""

import asyncio
import json
import sys
import argparse
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import tempfile
import shutil
import git

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from langchain.agents import AgentExecutor, create_openai_functions_agent
    from langchain.tools import Tool
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.schema import Document
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.vectorstores import Chroma
    from langchain.chains import RetrievalQA
    from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
    LANGCHAIN_AVAILABLE = True
except ImportError:
    logger.warning("LangChain not available, using mock implementations")
    LANGCHAIN_AVAILABLE = False

try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolExecutor
    from langgraph.checkpoint.sqlite import SqliteSaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    logger.warning("LangGraph not available")
    LANGGRAPH_AVAILABLE = False

try:
    from langsmith import Client as LangSmithClient
    LANGSMITH_AVAILABLE = True
except ImportError:
    logger.warning("LangSmith not available")
    LANGSMITH_AVAILABLE = False

class RAGGraphMemory:
    def __init__(self, persist_directory: str = "./rag_memory"):
        self.persist_directory = persist_directory
        self.embeddings = None
        self.vectorstore = None
        self.knowledge_graph = {}
        self.improvement_history = []
        
        if LANGCHAIN_AVAILABLE:
            self.embeddings = OpenAIEmbeddings()
            self.vectorstore = Chroma(
                persist_directory=persist_directory,
                embedding_function=self.embeddings
            )
    
    def add_documents(self, documents: List[Document], metadata: Dict[str, Any] = None):
        """Add documents to RAG memory with graph connections"""
        if not self.vectorstore:
            return {"success": False, "error": "Vector store not available"}
        
        try:
            # Add to vector store
            self.vectorstore.add_documents(documents)
            
            # Build knowledge graph connections
            for doc in documents:
                doc_id = doc.metadata.get('id', str(hash(doc.page_content)))
                self.knowledge_graph[doc_id] = {
                    'content': doc.page_content[:200],
                    'metadata': doc.metadata,
                    'connections': [],
                    'improvement_score': 1.0
                }
            
            return {"success": True, "documents_added": len(documents)}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def similarity_search(self, query: str, k: int = 5, threshold: float = 0.7):
        """Search with similarity threshold and graph enhancement"""
        if not self.vectorstore:
            return {"success": False, "error": "Vector store not available"}
        
        try:
            results = self.vectorstore.similarity_search_with_score(query, k=k)
            
            # Filter by threshold and enhance with graph connections
            filtered_results = []
            for doc, score in results:
                if score >= threshold:
                    doc_id = doc.metadata.get('id', str(hash(doc.page_content)))
                    graph_data = self.knowledge_graph.get(doc_id, {})
                    
                    filtered_results.append({
                        'content': doc.page_content,
                        'metadata': doc.metadata,
                        'similarity_score': score,
                        'graph_connections': graph_data.get('connections', []),
                        'improvement_score': graph_data.get('improvement_score', 1.0)
                    })
            
            return {
                "success": True,
                "results": filtered_results,
                "query": query,
                "total_results": len(filtered_results)
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def self_improve(self, feedback: Dict[str, Any]):
        """Self-improvement based on feedback"""
        try:
            improvement_id = len(self.improvement_history)
            
            # Analyze feedback and adjust improvement scores
            if feedback.get('accuracy_score', 0) > 0.85:
                # Positive feedback - boost related documents
                query = feedback.get('query', '')
                if query:
                    results = self.similarity_search(query, k=3, threshold=0.6)
                    for result in results.get('results', []):
                        doc_id = result['metadata'].get('id')
                        if doc_id in self.knowledge_graph:
                            current_score = self.knowledge_graph[doc_id]['improvement_score']
                            self.knowledge_graph[doc_id]['improvement_score'] = min(2.0, current_score * 1.1)
            
            self.improvement_history.append({
                'id': improvement_id,
                'feedback': feedback,
                'timestamp': feedback.get('timestamp'),
                'improvements_made': True
            })
            
            return {
                "success": True,
                "improvement_id": improvement_id,
                "improvements_applied": True
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

class RepositoryIngestor:
    def __init__(self, rag_memory: RAGGraphMemory):
        self.rag_memory = rag_memory
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\\n\\n", "\\n", " ", ""]
        ) if LANGCHAIN_AVAILABLE else None
    
    def ingest_repository(self, repo_url: str, local_path: Optional[str] = None):
        """Ingest a repository into RAG memory"""
        try:
            # Clone or copy repository
            if repo_url.startswith(('http', 'git')):
                # Clone from URL
                temp_dir = tempfile.mkdtemp()
                repo = git.Repo.clone_from(repo_url, temp_dir)
                repo_path = temp_dir
            else:
                # Local repository path
                repo_path = repo_url
            
            # Process repository files
            documents = []
            supported_extensions = {'.py', '.js', '.ts', '.md', '.txt', '.json', '.yaml', '.yml'}
            
            for file_path in Path(repo_path).rglob('*'):
                if file_path.is_file() and file_path.suffix in supported_extensions:
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        
                        # Split content into chunks
                        if self.text_splitter:
                            chunks = self.text_splitter.split_text(content)
                        else:
                            chunks = [content]
                        
                        for i, chunk in enumerate(chunks):
                            doc = Document(
                                page_content=chunk,
                                metadata={
                                    'id': f"{file_path.name}_{i}",
                                    'file_path': str(file_path.relative_to(repo_path)),
                                    'file_type': file_path.suffix,
                                    'chunk_index': i,
                                    'repo_url': repo_url,
                                    'total_chunks': len(chunks)
                                }
                            )
                            documents.append(doc)
                    
                    except Exception as e:
                        logger.warning(f"Failed to process {file_path}: {e}")
            
            # Add to RAG memory
            result = self.rag_memory.add_documents(documents)
            
            # Cleanup temporary directory
            if repo_url.startswith(('http', 'git')):
                shutil.rmtree(temp_dir)
            
            return {
                "success": True,
                "repo_url": repo_url,
                "documents_processed": len(documents),
                "files_processed": len(set(doc.metadata['file_path'] for doc in documents)),
                "rag_result": result
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

class SelfImprovingAgent:
    def __init__(self, rag_memory: RAGGraphMemory, agent_name: str = "SelfImprovingAgent"):
        self.rag_memory = rag_memory
        self.agent_name = agent_name
        self.llm = None
        self.agent = None
        self.memory = None
        
        if LANGCHAIN_AVAILABLE:
            self.llm = ChatOpenAI(temperature=0, model="gpt-4")
            self.memory = ConversationBufferWindowMemory(
                memory_key="chat_history",
                return_messages=True,
                k=10
            )
            self._create_agent()
    
    def _create_agent(self):
        """Create the self-improving agent with RAG tools"""
        if not LANGCHAIN_AVAILABLE:
            return
        
        # Create RAG tool
        rag_tool = Tool(
            name="rag_search",
            description="Search the knowledge base for relevant information",
            func=self._rag_search
        )
        
        # Create improvement tool
        improvement_tool = Tool(
            name="self_improve",
            description="Provide feedback for self-improvement",
            func=self._self_improve
        )
        
        tools = [rag_tool, improvement_tool]
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a self-improving AI agent with access to a RAG knowledge base.
            
            Your capabilities:
            1. Search the knowledge base for accurate information
            2. Provide factual responses based on retrieved context
            3. Self-improve based on feedback
            4. Avoid hallucination by only using retrieved information
            
            Always use the rag_search tool before answering questions.
            If you cannot find relevant information, say so explicitly.
            After providing an answer, use self_improve to learn from the interaction.
            """),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        self.agent = create_openai_functions_agent(self.llm, tools, prompt)
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True
        )
    
    def _rag_search(self, query: str) -> str:
        """RAG search tool function"""
        result = self.rag_memory.similarity_search(query, k=5, threshold=0.7)
        
        if result["success"] and result["results"]:
            context = "\\n\\n".join([
                f"Source: {r['metadata'].get('file_path', 'Unknown')}\\n{r['content']}"
                for r in result["results"]
            ])
            return f"Retrieved context:\\n{context}"
        else:
            return "No relevant information found in the knowledge base."
    
    def _self_improve(self, feedback: str) -> str:
        """Self-improvement tool function"""
        try:
            feedback_data = json.loads(feedback) if feedback.startswith('{') else {"feedback": feedback}
            result = self.rag_memory.self_improve(feedback_data)
            return f"Self-improvement applied: {result}"
        except Exception as e:
            return f"Self-improvement failed: {e}"
    
    def process_query(self, query: str, context: Dict[str, Any] = None):
        """Process a query with the self-improving agent"""
        if not self.agent_executor:
            return {
                "success": False,
                "error": "Agent not available",
                "response": "LangChain not configured"
            }
        
        try:
            response = self.agent_executor.invoke({"input": query})
            
            return {
                "success": True,
                "query": query,
                "response": response.get("output", ""),
                "agent_name": self.agent_name,
                "context_used": context or {}
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

async def main():
    parser = argparse.ArgumentParser(description='LangChain Agent Operations')
    parser.add_argument('operation', choices=[
        'ingest_repo', 'query_agent', 'rag_search', 'self_improve', 'create_agent'
    ])
    parser.add_argument('--repo-url', help='Repository URL or path to ingest')
    parser.add_argument('--query', help='Query for agent or RAG search')
    parser.add_argument('--agent-name', default='SelfImprovingAgent', help='Agent name')
    parser.add_argument('--feedback', help='Feedback for self-improvement')
    parser.add_argument('--threshold', type=float, default=0.7, help='Similarity threshold')
    
    args = parser.parse_args()
    
    # Initialize components
    rag_memory = RAGGraphMemory()
    repo_ingestor = RepositoryIngestor(rag_memory)
    
    try:
        if args.operation == 'ingest_repo':
            if not args.repo_url:
                print(json.dumps({'success': False, 'error': 'Repository URL required'}))
                return
            
            result = repo_ingestor.ingest_repository(args.repo_url)
            print(json.dumps(result))
        
        elif args.operation == 'query_agent':
            if not args.query:
                print(json.dumps({'success': False, 'error': 'Query required'}))
                return
            
            agent = SelfImprovingAgent(rag_memory, args.agent_name)
            result = agent.process_query(args.query)
            print(json.dumps(result))
        
        elif args.operation == 'rag_search':
            if not args.query:
                print(json.dumps({'success': False, 'error': 'Query required'}))
                return
            
            result = rag_memory.similarity_search(args.query, threshold=args.threshold)
            print(json.dumps(result))
        
        elif args.operation == 'self_improve':
            if not args.feedback:
                print(json.dumps({'success': False, 'error': 'Feedback required'}))
                return
            
            feedback_data = json.loads(args.feedback) if args.feedback.startswith('{') else {"feedback": args.feedback}
            result = rag_memory.self_improve(feedback_data)
            print(json.dumps(result))
        
        elif args.operation == 'create_agent':
            agent = SelfImprovingAgent(rag_memory, args.agent_name)
            result = {
                "success": True,
                "agent_name": args.agent_name,
                "capabilities": [
                    "rag_search",
                    "self_improvement",
                    "hallucination_prevention",
                    "context_aware_responses"
                ]
            }
            print(json.dumps(result))
    
    except Exception as e:
        print(json.dumps({'success': False, 'error': str(e)}))

if __name__ == '__main__':
    asyncio.run(main())
`;
    
    await fs.writeFile(this.config.langchainScript, pythonScript);
    console.log(chalk.green('✅ Created LangChain agent script'));
  }

  async initializeRAGGraphMemory() {
    // Initialize in-memory RAG graph structure
    this.ragGraphs.set('default', {
      nodes: new Map(),
      edges: new Map(),
      embeddings: new Map(),
      improvementHistory: [],
      lastUpdated: Date.now()
    });
    
    console.log(chalk.green('✅ RAG graph memory initialized'));
  }

  start() {
    this.wss = new WebSocketServer({ 
      port: this.port,
      perMessageDeflate: false
    });
    
    console.log(chalk.cyan(`🧠 LangChain MCP Server started on port ${this.port}`));
    console.log(chalk.yellow('🔗 Ready to provide advanced AI agent capabilities'));
    
    this.wss.on('connection', (ws, req) => {
      const clientId = this.generateClientId();
      this.clients.set(clientId, {
        ws,
        connectedAt: new Date(),
        agentOperations: 0,
        activeAgents: new Set()
      });
      
      console.log(chalk.green(`✅ Client connected: ${clientId}`));
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          await this.handleMessage(clientId, message);
        } catch (error) {
          console.error(chalk.red('❌ Invalid message format:'), error.message);
          this.sendError(clientId, 'Invalid message format', error.message);
        }
      });
      
      ws.on('close', () => {
        this.cleanupClient(clientId);
        console.log(chalk.yellow(`👋 Client disconnected: ${clientId}`));
      });
      
      ws.on('error', (error) => {
        console.error(chalk.red(`❌ WebSocket error for ${clientId}:`), error.message);
      });
      
      // Send welcome message
      this.sendMessage(clientId, {
        type: 'welcome',
        message: 'Connected to LangChain MCP Server',
        capabilities: [
          'repository_ingestion',
          'rag_graph_memory',
          'self_improving_agents',
          'hallucination_prevention',
          'langgraph_workflows',
          'langsmith_monitoring',
          'multi_modal_processing',
          'knowledge_graph_building'
        ],
        langchainConfigured: !!this.config.openaiApiKey,
        langsmithEnabled: !!this.config.langsmithApiKey
      });
    });
  }

  generateClientId() {
    return `langchain_client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async handleMessage(clientId, message) {
    console.log(chalk.blue(`📨 LangChain request from ${clientId}:`), message.type);
    
    try {
      switch (message.type) {
        case 'ingest_repository':
          await this.handleIngestRepository(clientId, message);
          break;
        case 'create_agent':
          await this.handleCreateAgent(clientId, message);
          break;
        case 'query_agent':
          await this.handleQueryAgent(clientId, message);
          break;
        case 'rag_search':
          await this.handleRAGSearch(clientId, message);
          break;
        case 'self_improve':
          await this.handleSelfImprove(clientId, message);
          break;
        case 'build_knowledge_graph':
          await this.handleBuildKnowledgeGraph(clientId, message);
          break;
        case 'get_agent_status':
          await this.handleGetAgentStatus(clientId, message);
          break;
        case 'langgraph_workflow':
          await this.handleLangGraphWorkflow(clientId, message);
          break;
        default:
          console.log(chalk.yellow(`⚠️ Unknown message type: ${message.type}`));
          this.sendError(clientId, 'Unknown message type', message.type);
      }
    } catch (error) {
      console.error(chalk.red(`❌ Error handling ${message.type}:`), error.message);
      this.sendError(clientId, `Error handling ${message.type}`, error.message);
    }
  }

  async handleIngestRepository(clientId, message) {
    const { repoUrl, agentName = 'default', options = {} } = message;
    
    try {
      console.log(chalk.blue(`🔄 Ingesting repository: ${repoUrl}`));
      
      // Execute Python script for repository ingestion
      const result = await this.executeLangChainScript('ingest_repo', {
        'repo-url': repoUrl
      });
      
      // Update client stats
      const client = this.clients.get(clientId);
      if (client) {
        client.agentOperations++;
      }
      
      this.sendMessage(clientId, {
        type: 'repository_ingested',
        requestId: message.requestId,
        repoUrl,
        result,
        agentName
      });
      
    } catch (error) {
      this.sendError(clientId, 'Repository ingestion failed', error.message);
    }
  }

  async executeLangChainScript(operation, params) {
    return new Promise((resolve, reject) => {
      const args = [this.config.langchainScript, operation];
      
      // Add parameters
      for (const [key, value] of Object.entries(params)) {
        args.push(`--${key}`, value);
      }
      
      const process = spawn(this.config.pythonPath, args, {
        stdio: 'pipe',
        env: {
          ...process.env,
          OPENAI_API_KEY: this.config.openaiApiKey,
          LANGSMITH_API_KEY: this.config.langsmithApiKey,
          LANGSMITH_PROJECT: this.config.langsmithProject
        }
      });
      
      let stdout = '';
      let stderr = '';
      
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout);
            resolve(result);
          } catch (parseError) {
            reject(new Error(`Failed to parse LangChain output: ${parseError.message}`));
          }
        } else {
          reject(new Error(`LangChain script failed: ${stderr}`));
        }
      });
      
      process.on('error', (error) => {
        reject(new Error(`Failed to execute LangChain script: ${error.message}`));
      });
      
      // Set timeout
      setTimeout(() => {
        process.kill();
        reject(new Error('LangChain script timeout'));
      }, 120000); // 2 minutes timeout
    });
  }

  sendMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === 1) { // WebSocket.OPEN
      client.ws.send(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
        server: 'langchain-mcp'
      }));
    }
  }

  sendError(clientId, type, message) {
    this.sendMessage(clientId, {
      type: 'error',
      error: type,
      message
    });
  }

  cleanupClient(clientId) {
    const client = this.clients.get(clientId);
    if (client) {
      // Clean up any active agents for this client
      client.activeAgents.forEach(agentId => {
        this.agentCache.delete(agentId);
      });
    }
    this.clients.delete(clientId);
  }

  stop() {
    if (this.wss) {
      this.wss.close();
      console.log(chalk.yellow('🛑 LangChain MCP Server stopped'));
    }
  }
}

// Start server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new LangChainMCPServer();
  server.start();
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n🛑 Shutting down LangChain MCP server...'));
    server.stop();
    process.exit(0);
  });
}

export default LangChainMCPServer;
