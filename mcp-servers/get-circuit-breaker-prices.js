#!/usr/bin/env node

import MC<PERSON>lient from './client/mcp-client.js';
import chalk from 'chalk';

async function getCircuitBreakerPrices() {
  console.log(chalk.cyan('🔌 Getting Circuit Breaker Prices from ARB.co.za...'));
  
  const client = new MCPClient();
  
  try {
    await client.connectToServer('api');
    await new Promise(r => setTimeout(r, 2000));
    
    console.log(chalk.blue('🔍 Searching for circuit breaker categories...'));
    
    // First, let's search for circuit breaker related URLs
    const searchResult = await client.scrapeWeb('https://arb.co.za', 'a[href*="circuit"], a[href*="breaker"], a[href*="mcb"], a[href*="mccb"]', 'links');
    
    console.log(chalk.green(`✅ Found ${searchResult.data.length} circuit breaker related links`));
    
    // Look for specific circuit breaker categories
    const circuitBreakerUrls = [];
    
    // Try common circuit breaker category URLs
    const potentialUrls = [
      'https://arb.co.za/product-category/switchgear/miniature-circuit-breakers/',
      'https://arb.co.za/product-category/switchgear/moulded-case-circuit-breakers/',
      'https://arb.co.za/product-category/switchgear/',
      'https://arb.co.za/products/?search=circuit+breaker',
      'https://arb.co.za/products/?search=mcb',
      'https://arb.co.za/products/?search=mccb'
    ];
    
    console.log(chalk.blue('🔍 Checking circuit breaker category pages...'));
    
    for (const url of potentialUrls) {
      try {
        console.log(chalk.gray(`Checking: ${url}`));
        
        const pageResult = await client.scrapeWeb(url, null, 'data');
        
        if (pageResult.data && pageResult.data.title && !pageResult.data.title.includes('404')) {
          console.log(chalk.green(`✅ Found valid page: ${pageResult.data.title}`));
          
          // Extract product information from this page
          const productResult = await client.scrapeWeb(url, '.product, .woocommerce-loop-product__title, .price, .woocommerce-Price-amount', 'data');
          
          if (productResult.data && productResult.data.links) {
            console.log(chalk.cyan(`\n📦 Products found on ${url}:`));
            console.log(chalk.white(`📊 Total products: ${productResult.data.links.length}`));
            
            // Look for price information
            const priceResult = await client.scrapeWeb(url, '.price, .woocommerce-Price-amount, .amount, .product-price', 'text');
            
            if (priceResult.data && priceResult.data.length > 0) {
              console.log(chalk.cyan('\n💰 Prices found:'));
              priceResult.data.forEach((price, index) => {
                if (price.trim() && (price.includes('R') || price.includes('.'))) {
                  console.log(chalk.white(`  ${index + 1}. ${price.trim()}`));
                }
              });
            }
            
            // Look for product titles
            const titleResult = await client.scrapeWeb(url, '.woocommerce-loop-product__title, .product-title, h2.product-name, .entry-title', 'text');
            
            if (titleResult.data && titleResult.data.length > 0) {
              console.log(chalk.cyan('\n🔌 Circuit Breaker Products:'));
              titleResult.data.slice(0, 10).forEach((title, index) => {
                if (title.trim() && (title.toLowerCase().includes('circuit') || title.toLowerCase().includes('breaker') || title.toLowerCase().includes('mcb'))) {
                  console.log(chalk.white(`  ${index + 1}. ${title.trim()}`));
                }
              });
            }
          }
        }
        
        // Add delay between requests
        await new Promise(r => setTimeout(r, 1000));
        
      } catch (error) {
        console.log(chalk.yellow(`⚠️ Could not access ${url}: ${error.message}`));
      }
    }
    
    // Try searching the main products page for circuit breakers
    console.log(chalk.blue('\n🔍 Searching main products page...'));
    
    try {
      const mainProductsResult = await client.scrapeWeb('https://arb.co.za/products/', null, 'data');
      
      if (mainProductsResult.data) {
        console.log(chalk.green('✅ Main products page accessed'));
        
        // Look for circuit breaker products in search results
        const searchTerms = ['circuit breaker', 'mcb', 'miniature circuit breaker', 'moulded case circuit breaker'];
        
        for (const term of searchTerms) {
          try {
            console.log(chalk.gray(`Searching for: ${term}`));
            
            const searchUrl = `https://arb.co.za/products/?search=${encodeURIComponent(term)}`;
            const searchPageResult = await client.scrapeWeb(searchUrl, null, 'data');
            
            if (searchPageResult.data && searchPageResult.data.links) {
              console.log(chalk.cyan(`\n🔍 Search results for "${term}":`));
              console.log(chalk.white(`📊 Results found: ${searchPageResult.data.links.length}`));
              
              // Extract product details from search results
              const productDetailsResult = await client.scrapeWeb(searchUrl, '.product-item, .product, .woocommerce-loop-product', 'data');
              
              if (productDetailsResult.data) {
                // Look for prices in search results
                const searchPricesResult = await client.scrapeWeb(searchUrl, '.price, .woocommerce-Price-amount, .product-price', 'text');
                
                if (searchPricesResult.data && searchPricesResult.data.length > 0) {
                  console.log(chalk.cyan('💰 Prices in search results:'));
                  searchPricesResult.data.forEach((price, index) => {
                    if (price.trim() && (price.includes('R') || /\d+\.\d+/.test(price))) {
                      console.log(chalk.white(`  ${index + 1}. ${price.trim()}`));
                    }
                  });
                }
                
                // Look for product names in search results
                const searchTitlesResult = await client.scrapeWeb(searchUrl, '.product-title, .woocommerce-loop-product__title, h2, h3', 'text');
                
                if (searchTitlesResult.data && searchTitlesResult.data.length > 0) {
                  console.log(chalk.cyan('🔌 Products in search results:'));
                  searchTitlesResult.data.slice(0, 10).forEach((title, index) => {
                    if (title.trim() && title.length > 5) {
                      console.log(chalk.white(`  ${index + 1}. ${title.trim()}`));
                    }
                  });
                }
              }
            }
            
            await new Promise(r => setTimeout(r, 1500));
            
          } catch (error) {
            console.log(chalk.yellow(`⚠️ Search failed for "${term}": ${error.message}`));
          }
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️ Could not access main products page: ${error.message}`));
    }
    
    // Try to get switchgear category page
    console.log(chalk.blue('\n🔍 Checking switchgear category...'));
    
    try {
      const switchgearUrl = 'https://arb.co.za/product-category/switchgear/';
      const switchgearResult = await client.scrapeWeb(switchgearUrl, null, 'data');
      
      if (switchgearResult.data) {
        console.log(chalk.green('✅ Switchgear category page found'));
        console.log(chalk.white(`📊 Page title: ${switchgearResult.data.title}`));
        
        // Get all product links from switchgear category
        const switchgearLinksResult = await client.scrapeWeb(switchgearUrl, 'a[href*="product"]', 'links');
        
        if (switchgearLinksResult.data && switchgearLinksResult.data.length > 0) {
          console.log(chalk.cyan(`\n🔗 Found ${switchgearLinksResult.data.length} product links in switchgear`));
          
          // Filter for circuit breaker related products
          const circuitBreakerLinks = switchgearLinksResult.data.filter(link => 
            link.href && (
              link.href.includes('circuit') || 
              link.href.includes('breaker') || 
              link.href.includes('mcb') ||
              link.text.toLowerCase().includes('circuit') ||
              link.text.toLowerCase().includes('breaker')
            )
          );
          
          console.log(chalk.cyan(`🔌 Circuit breaker related links: ${circuitBreakerLinks.length}`));
          
          // Visit first few circuit breaker product pages to get prices
          for (let i = 0; i < Math.min(5, circuitBreakerLinks.length); i++) {
            const link = circuitBreakerLinks[i];
            try {
              console.log(chalk.gray(`Checking product: ${link.text}`));
              
              const productPageResult = await client.scrapeWeb(link.href, null, 'data');
              
              if (productPageResult.data) {
                // Get price from product page
                const productPriceResult = await client.scrapeWeb(link.href, '.price, .woocommerce-Price-amount, .product-price, .single-product-price', 'text');
                
                if (productPriceResult.data && productPriceResult.data.length > 0) {
                  console.log(chalk.green(`💰 ${link.text}:`));
                  productPriceResult.data.forEach(price => {
                    if (price.trim() && (price.includes('R') || /\d+\.\d+/.test(price))) {
                      console.log(chalk.white(`    Price: ${price.trim()}`));
                    }
                  });
                }
              }
              
              await new Promise(r => setTimeout(r, 1000));
              
            } catch (error) {
              console.log(chalk.yellow(`⚠️ Could not access product ${link.text}: ${error.message}`));
            }
          }
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️ Could not access switchgear category: ${error.message}`));
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Circuit breaker price extraction failed:'), error.message);
  } finally {
    await client.disconnect();
  }
}

getCircuitBreakerPrices();
