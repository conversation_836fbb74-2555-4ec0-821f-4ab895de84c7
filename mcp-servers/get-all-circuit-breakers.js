#!/usr/bin/env node

import MC<PERSON>lient from './client/mcp-client.js';
import chalk from 'chalk';

async function getAllCircuitBreakers() {
  console.log(chalk.cyan('🔌 Getting ALL Circuit Breaker Prices from ARB.co.za (Complete Extraction)...'));
  
  const client = new MCPClient();
  let allCircuitBreakers = [];
  let totalCount = 0;
  
  try {
    await client.connectToServer('api');
    await new Promise(r => setTimeout(r, 2000));
    
    const categories = [
      {
        url: 'https://arb.co.za/product-category/switchgear/miniature-circuit-breakers/',
        name: 'Miniature Circuit Breakers (MCB)',
        baseUrl: 'https://arb.co.za/product-category/switchgear/miniature-circuit-breakers'
      },
      {
        url: 'https://arb.co.za/product-category/switchgear/moulded-case-circuit-breakers/',
        name: 'Moulded Case Circuit Breakers (MCCB)',
        baseUrl: 'https://arb.co.za/product-category/switchgear/moulded-case-circuit-breakers'
      }
    ];
    
    for (const category of categories) {
      console.log(chalk.blue(`\n🔍 Processing ALL pages for ${category.name}...`));
      
      let currentPage = 1;
      let hasMorePages = true;
      let categoryProducts = [];
      
      while (hasMorePages) {
        const pageUrl = currentPage === 1 ? category.url : `${category.baseUrl}/page/${currentPage}/`;
        
        console.log(chalk.gray(`📄 Scraping page ${currentPage}: ${pageUrl}`));
        
        try {
          // Get the page data
          const pageResult = await client.scrapeWeb(pageUrl, null, 'data');
          
          if (!pageResult.data || pageResult.data.title.includes('404') || pageResult.data.title.includes('Page not found')) {
            console.log(chalk.yellow(`⚠️ Page ${currentPage} not found, stopping pagination`));
            hasMorePages = false;
            break;
          }
          
          console.log(chalk.green(`✅ Page ${currentPage} loaded: ${pageResult.data.title}`));
          
          // Extract all product information from this page
          const productLinksResult = await client.scrapeWeb(pageUrl, 'a[href*="/product/"]', 'links');
          
          if (!productLinksResult.data || productLinksResult.data.length === 0) {
            console.log(chalk.yellow(`⚠️ No products found on page ${currentPage}, stopping`));
            hasMorePages = false;
            break;
          }
          
          console.log(chalk.cyan(`📦 Found ${productLinksResult.data.length} product links on page ${currentPage}`));
          
          // Get product titles from this page
          const titlesResult = await client.scrapeWeb(pageUrl, '.woocommerce-loop-product__title, .product-title, h2.entry-title, .entry-title, h3', 'text');
          
          // Get prices from this page
          const pricesResult = await client.scrapeWeb(pageUrl, '.price, .woocommerce-Price-amount, .amount, .product-price, span[class*="price"]', 'text');
          
          // Get stock status
          const stockResult = await client.scrapeWeb(pageUrl, '.stock, .in-stock, .out-of-stock, .stock-status', 'text');
          
          // Process products from this page
          const pageProducts = [];
          
          if (titlesResult.data && titlesResult.data.length > 0) {
            console.log(chalk.cyan(`\n🔌 Products on page ${currentPage}:`));
            
            for (let i = 0; i < titlesResult.data.length; i++) {
              const title = titlesResult.data[i]?.trim();
              if (title && title.length > 3 && !title.includes('Sort by') && !title.includes('Filter')) {
                
                const product = {
                  title: title,
                  category: category.name,
                  page: currentPage,
                  price: null,
                  stock: null,
                  url: null
                };
                
                // Try to match with price
                if (pricesResult.data && pricesResult.data.length > i) {
                  const price = pricesResult.data[i]?.trim();
                  if (price && (price.includes('R') || /\d+\.\d+/.test(price)) && price.length < 50) {
                    product.price = price;
                  }
                }
                
                // Try to match with stock
                if (stockResult.data && stockResult.data.length > i) {
                  const stock = stockResult.data[i]?.trim();
                  if (stock && (stock.includes('stock') || stock.includes('available'))) {
                    product.stock = stock;
                  }
                }
                
                // Try to match with URL
                if (productLinksResult.data && productLinksResult.data.length > i) {
                  product.url = productLinksResult.data[i]?.href;
                }
                
                pageProducts.push(product);
                categoryProducts.push(product);
                
                console.log(chalk.white(`  ${categoryProducts.length}. ${title}`));
                if (product.price) {
                  console.log(chalk.green(`     💰 Price: ${product.price}`));
                }
                if (product.stock) {
                  console.log(chalk.blue(`     📦 Stock: ${product.stock}`));
                }
              }
            }
          }
          
          // If we found fewer products than expected, we might be at the last page
          if (pageProducts.length < 10) {
            console.log(chalk.yellow(`⚠️ Only ${pageProducts.length} products on page ${currentPage}, might be last page`));
          }
          
          // Check for pagination indicators
          const paginationResult = await client.scrapeWeb(pageUrl, '.next, .page-numbers, .pagination a, a[rel="next"]', 'links');
          
          let foundNextPage = false;
          if (paginationResult.data && paginationResult.data.length > 0) {
            for (const link of paginationResult.data) {
              if (link.href && (link.href.includes(`page/${currentPage + 1}`) || link.text?.includes('Next') || link.text?.includes('→'))) {
                foundNextPage = true;
                break;
              }
            }
          }
          
          if (!foundNextPage && pageProducts.length < 5) {
            console.log(chalk.yellow(`⚠️ No next page found and few products, stopping pagination`));
            hasMorePages = false;
          } else {
            currentPage++;
            
            // Safety limit to prevent infinite loops
            if (currentPage > 20) {
              console.log(chalk.yellow(`⚠️ Reached page limit (20), stopping`));
              hasMorePages = false;
            }
          }
          
          // Add delay between page requests
          await new Promise(r => setTimeout(r, 2000));
          
        } catch (error) {
          console.log(chalk.red(`❌ Error on page ${currentPage}: ${error.message}`));
          hasMorePages = false;
        }
      }
      
      console.log(chalk.green(`\n✅ Completed ${category.name}: ${categoryProducts.length} products found across ${currentPage - 1} pages`));
      allCircuitBreakers = allCircuitBreakers.concat(categoryProducts);
      totalCount += categoryProducts.length;
      
      // Add delay between categories
      await new Promise(r => setTimeout(r, 3000));
    }
    
    // Summary Report
    console.log(chalk.cyan('\n📊 COMPLETE CIRCUIT BREAKER EXTRACTION SUMMARY'));
    console.log(chalk.white('=' .repeat(60)));
    console.log(chalk.green(`🎯 TOTAL CIRCUIT BREAKERS FOUND: ${totalCount}`));
    console.log(chalk.white('=' .repeat(60)));
    
    // Breakdown by category
    const mcbCount = allCircuitBreakers.filter(p => p.category.includes('MCB')).length;
    const mccbCount = allCircuitBreakers.filter(p => p.category.includes('MCCB')).length;
    
    console.log(chalk.cyan(`⚡ Miniature Circuit Breakers (MCB): ${mcbCount}`));
    console.log(chalk.cyan(`🏭 Moulded Case Circuit Breakers (MCCB): ${mccbCount}`));
    
    // Price analysis
    const productsWithPrices = allCircuitBreakers.filter(p => p.price && p.price !== 'R0.00');
    console.log(chalk.yellow(`💰 Products with pricing: ${productsWithPrices.length}`));
    
    if (productsWithPrices.length > 0) {
      console.log(chalk.cyan('\n💰 PRICE BREAKDOWN:'));
      
      // MCB prices
      const mcbPrices = productsWithPrices.filter(p => p.category.includes('MCB'));
      if (mcbPrices.length > 0) {
        console.log(chalk.white(`\n⚡ MCB Prices (${mcbPrices.length} products):`));
        mcbPrices.slice(0, 10).forEach((product, index) => {
          console.log(chalk.white(`  ${index + 1}. ${product.title.substring(0, 40)}... - ${product.price}`));
        });
        if (mcbPrices.length > 10) {
          console.log(chalk.gray(`  ... and ${mcbPrices.length - 10} more MCB products`));
        }
      }
      
      // MCCB prices
      const mccbPrices = productsWithPrices.filter(p => p.category.includes('MCCB'));
      if (mccbPrices.length > 0) {
        console.log(chalk.white(`\n🏭 MCCB Prices (${mccbPrices.length} products):`));
        mccbPrices.slice(0, 10).forEach((product, index) => {
          console.log(chalk.white(`  ${index + 1}. ${product.title.substring(0, 40)}... - ${product.price}`));
        });
        if (mccbPrices.length > 10) {
          console.log(chalk.gray(`  ... and ${mccbPrices.length - 10} more MCCB products`));
        }
      }
    }
    
    console.log(chalk.green('\n🎉 COMPLETE EXTRACTION FINISHED!'));
    console.log(chalk.white(`📊 Total Circuit Breakers: ${totalCount}`));
    console.log(chalk.white(`💰 Products with Prices: ${productsWithPrices.length}`));
    console.log(chalk.white(`🔗 Products with URLs: ${allCircuitBreakers.filter(p => p.url).length}`));
    
  } catch (error) {
    console.log(chalk.red('❌ Complete circuit breaker extraction failed:'), error.message);
  } finally {
    await client.disconnect();
  }
}

getAllCircuitBreakers();
