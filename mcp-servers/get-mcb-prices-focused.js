#!/usr/bin/env node

import MC<PERSON>lient from './client/mcp-client.js';
import chalk from 'chalk';

async function getMCBPricesFocused() {
  console.log(chalk.cyan('🔌 Getting Circuit Breaker Prices from ARB.co.za (Focused Approach)...'));
  
  const client = new MCPClient();
  
  try {
    await client.connectToServer('api');
    await new Promise(r => setTimeout(r, 2000));
    
    // Focus on the specific category pages we know exist
    const categoryUrls = [
      {
        url: 'https://arb.co.za/product-category/switchgear/miniature-circuit-breakers/',
        name: 'Miniature Circuit Breakers (MCB)'
      },
      {
        url: 'https://arb.co.za/product-category/switchgear/moulded-case-circuit-breakers/',
        name: 'Moulded Case Circuit Breakers (MCCB)'
      }
    ];
    
    for (const category of categoryUrls) {
      console.log(chalk.blue(`\n🔍 Processing ${category.name}...`));
      console.log(chalk.gray(`URL: ${category.url}`));
      
      try {
        // Get the category page data
        const pageResult = await client.scrapeWeb(category.url, null, 'data');
        
        if (pageResult.data) {
          console.log(chalk.green(`✅ Successfully accessed ${category.name} page`));
          console.log(chalk.white(`📊 Page Title: ${pageResult.data.title}`));
          
          // Extract product information
          console.log(chalk.blue('🔍 Extracting product information...'));
          
          // Look for product containers and prices
          const productResult = await client.scrapeWeb(category.url, '.product, .woocommerce-loop-product, .product-item', 'data');
          
          if (productResult.data && productResult.data.links) {
            console.log(chalk.cyan(`📦 Found ${productResult.data.links.length} product links`));
            
            // Get product titles
            const titleResult = await client.scrapeWeb(category.url, '.woocommerce-loop-product__title, .product-title, h2.entry-title, .entry-title', 'text');
            
            if (titleResult.data && titleResult.data.length > 0) {
              console.log(chalk.cyan(`\n🔌 ${category.name} Products:`));
              titleResult.data.slice(0, 15).forEach((title, index) => {
                if (title.trim() && title.length > 3) {
                  console.log(chalk.white(`  ${index + 1}. ${title.trim()}`));
                }
              });
            }
            
            // Get prices
            const priceResult = await client.scrapeWeb(category.url, '.price, .woocommerce-Price-amount, .amount, .product-price, .price-current', 'text');
            
            if (priceResult.data && priceResult.data.length > 0) {
              console.log(chalk.cyan(`\n💰 ${category.name} Prices:`));
              const prices = priceResult.data.filter(price => 
                price.trim() && 
                (price.includes('R') || /\d+\.\d+/.test(price)) &&
                price.length < 50
              );
              
              prices.slice(0, 20).forEach((price, index) => {
                console.log(chalk.white(`  ${index + 1}. ${price.trim()}`));
              });
              
              if (prices.length > 20) {
                console.log(chalk.gray(`  ... and ${prices.length - 20} more prices`));
              }
            }
            
            // Try to get product links for individual product pages
            const productLinks = productResult.data.links.filter(link => 
              link.href && 
              link.href.includes('product') && 
              !link.href.includes('category')
            ).slice(0, 5);
            
            if (productLinks.length > 0) {
              console.log(chalk.blue(`\n🔍 Checking individual product pages (first 5)...`));
              
              for (let i = 0; i < productLinks.length; i++) {
                const productLink = productLinks[i];
                try {
                  console.log(chalk.gray(`Checking: ${productLink.text || 'Product ' + (i + 1)}`));
                  
                  const individualProductResult = await client.scrapeWeb(productLink.href, null, 'data');
                  
                  if (individualProductResult.data) {
                    // Get detailed price from individual product page
                    const detailedPriceResult = await client.scrapeWeb(productLink.href, '.price, .woocommerce-Price-amount, .single-product-price, .product-price', 'text');
                    
                    if (detailedPriceResult.data && detailedPriceResult.data.length > 0) {
                      const productPrices = detailedPriceResult.data.filter(price => 
                        price.trim() && 
                        (price.includes('R') || /\d+\.\d+/.test(price))
                      );
                      
                      if (productPrices.length > 0) {
                        console.log(chalk.green(`  💰 ${productLink.text || 'Product'}: ${productPrices[0].trim()}`));
                      }
                    }
                    
                    // Get product specifications
                    const specsResult = await client.scrapeWeb(productLink.href, '.product-specs, .specifications, .product-details, .woocommerce-product-details__short-description', 'text');
                    
                    if (specsResult.data && specsResult.data.length > 0) {
                      const specs = specsResult.data[0].trim().substring(0, 100);
                      if (specs) {
                        console.log(chalk.gray(`    📋 Specs: ${specs}...`));
                      }
                    }
                  }
                  
                  // Add delay between requests
                  await new Promise(r => setTimeout(r, 1500));
                  
                } catch (error) {
                  console.log(chalk.yellow(`    ⚠️ Could not access individual product: ${error.message}`));
                }
              }
            }
          }
          
          // Try alternative selectors for prices
          console.log(chalk.blue('🔍 Trying alternative price selectors...'));
          
          const altPriceResult = await client.scrapeWeb(category.url, 'span[class*="price"], div[class*="price"], .cost, .value, .amount', 'text');
          
          if (altPriceResult.data && altPriceResult.data.length > 0) {
            const altPrices = altPriceResult.data.filter(price => 
              price.trim() && 
              (price.includes('R') || /\d+\.\d+/.test(price)) &&
              price.length < 30
            );
            
            if (altPrices.length > 0) {
              console.log(chalk.cyan(`\n💰 Additional Prices Found:`));
              altPrices.slice(0, 10).forEach((price, index) => {
                console.log(chalk.white(`  ${index + 1}. ${price.trim()}`));
              });
            }
          }
        }
        
        // Add delay between category requests
        await new Promise(r => setTimeout(r, 2000));
        
      } catch (error) {
        console.log(chalk.red(`❌ Error processing ${category.name}: ${error.message}`));
      }
    }
    
    // Summary
    console.log(chalk.cyan('\n📊 Circuit Breaker Price Extraction Summary:'));
    console.log(chalk.white('✅ Successfully accessed ARB circuit breaker categories'));
    console.log(chalk.white('✅ Found Miniature Circuit Breakers (MCB) section'));
    console.log(chalk.white('✅ Found Moulded Case Circuit Breakers (MCCB) section'));
    console.log(chalk.white('💡 Prices are available but may require account login for full details'));
    
  } catch (error) {
    console.log(chalk.red('❌ Circuit breaker price extraction failed:'), error.message);
  } finally {
    await client.disconnect();
  }
}

getMCBPricesFocused();
