{"version": 3, "names": ["_index", "require", "_index2", "toBlock", "node", "parent", "isBlockStatement", "blockNodes", "isEmptyStatement", "isStatement", "isFunction", "returnStatement", "expressionStatement", "blockStatement"], "sources": ["../../src/converters/toBlock.ts"], "sourcesContent": ["import {\n  isBlockStatement,\n  isFunction,\n  isEmptyStatement,\n  isStatement,\n} from \"../validators/generated/index.ts\";\nimport {\n  returnStatement,\n  expressionStatement,\n  blockStatement,\n} from \"../builders/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function toBlock(\n  node: t.Statement | t.Expression,\n  parent?: t.Node,\n): t.BlockStatement {\n  if (isBlockStatement(node)) {\n    return node;\n  }\n\n  let blockNodes: t.Statement[] = [];\n\n  if (isEmptyStatement(node)) {\n    blockNodes = [];\n  } else {\n    if (!isStatement(node)) {\n      if (isFunction(parent)) {\n        node = returnStatement(node);\n      } else {\n        node = expressionStatement(node);\n      }\n    }\n\n    blockNodes = [node];\n  }\n\n  return blockStatement(blockNodes);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,OAAA,GAAAD,OAAA;AAOe,SAASE,OAAOA,CAC7BC,IAAgC,EAChCC,MAAe,EACG;EAClB,IAAI,IAAAC,uBAAgB,EAACF,IAAI,CAAC,EAAE;IAC1B,OAAOA,IAAI;EACb;EAEA,IAAIG,UAAyB,GAAG,EAAE;EAElC,IAAI,IAAAC,uBAAgB,EAACJ,IAAI,CAAC,EAAE;IAC1BG,UAAU,GAAG,EAAE;EACjB,CAAC,MAAM;IACL,IAAI,CAAC,IAAAE,kBAAW,EAACL,IAAI,CAAC,EAAE;MACtB,IAAI,IAAAM,iBAAU,EAACL,MAAM,CAAC,EAAE;QACtBD,IAAI,GAAG,IAAAO,uBAAe,EAACP,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLA,IAAI,GAAG,IAAAQ,2BAAmB,EAACR,IAAI,CAAC;MAClC;IACF;IAEAG,UAAU,GAAG,CAACH,IAAI,CAAC;EACrB;EAEA,OAAO,IAAAS,sBAAc,EAACN,UAAU,CAAC;AACnC", "ignoreList": []}