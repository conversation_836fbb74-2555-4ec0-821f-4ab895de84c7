{"version": 3, "names": ["_getBindingIdentifiers", "require", "_index", "_index2", "_productions", "_cloneNode", "gatherSequenceExpressions", "nodes", "declars", "exprs", "ensureLastUndefined", "node", "isEmptyStatement", "isExpression", "push", "isExpressionStatement", "expression", "isVariableDeclaration", "kind", "declar", "declarations", "bindings", "getBindingIdentifiers", "key", "Object", "keys", "id", "cloneNode", "init", "assignmentExpression", "isIfStatement", "consequent", "buildUndefinedNode", "alternate", "conditionalExpression", "test", "isBlockStatement", "body", "indexOf", "length", "sequenceExpression"], "sources": ["../../src/converters/gatherSequenceExpressions.ts"], "sourcesContent": ["// TODO(Babel 8) Remove this file\nif (process.env.BABEL_8_BREAKING && process.env.IS_PUBLISH) {\n  throw new Error(\n    \"Internal Babel error: This file should only be loaded in Babel 7\",\n  );\n}\n\nimport getBindingIdentifiers from \"../retrievers/getBindingIdentifiers.ts\";\nimport {\n  isExpression,\n  isExpressionStatement,\n  isVariableDeclaration,\n  isIfStatement,\n  isBlockStatement,\n  isEmptyStatement,\n} from \"../validators/generated/index.ts\";\nimport {\n  sequenceExpression,\n  assignmentExpression,\n  conditionalExpression,\n} from \"../builders/generated/index.ts\";\nimport { buildUndefinedNode } from \"../builders/productions.ts\";\nimport cloneNode from \"../clone/cloneNode.ts\";\nimport type * as t from \"../index.ts\";\n\nexport type DeclarationInfo = {\n  kind: t.VariableDeclaration[\"kind\"];\n  id: t.Identifier;\n};\n\nexport default function gatherSequenceExpressions(\n  nodes: ReadonlyArray<t.Node>,\n  declars: Array<DeclarationInfo>,\n) {\n  const exprs: t.Expression[] = [];\n  let ensureLastUndefined = true;\n\n  for (const node of nodes) {\n    // if we encounter emptyStatement before a non-emptyStatement\n    // we want to disregard that\n    if (!isEmptyStatement(node)) {\n      ensureLastUndefined = false;\n    }\n\n    if (isExpression(node)) {\n      exprs.push(node);\n    } else if (isExpressionStatement(node)) {\n      exprs.push(node.expression);\n    } else if (isVariableDeclaration(node)) {\n      if (node.kind !== \"var\") return; // bailed\n\n      for (const declar of node.declarations) {\n        const bindings = getBindingIdentifiers(declar);\n        for (const key of Object.keys(bindings)) {\n          declars.push({\n            kind: node.kind,\n            id: cloneNode(bindings[key]),\n          });\n        }\n\n        if (declar.init) {\n          exprs.push(assignmentExpression(\"=\", declar.id, declar.init));\n        }\n      }\n\n      ensureLastUndefined = true;\n    } else if (isIfStatement(node)) {\n      const consequent = node.consequent\n        ? gatherSequenceExpressions([node.consequent], declars)\n        : buildUndefinedNode();\n      const alternate = node.alternate\n        ? gatherSequenceExpressions([node.alternate], declars)\n        : buildUndefinedNode();\n      if (!consequent || !alternate) return; // bailed\n\n      exprs.push(conditionalExpression(node.test, consequent, alternate));\n    } else if (isBlockStatement(node)) {\n      const body = gatherSequenceExpressions(node.body, declars);\n      if (!body) return; // bailed\n\n      exprs.push(body);\n    } else if (isEmptyStatement(node)) {\n      // empty statement so ensure the last item is undefined if we're last\n      // checks if emptyStatement is first\n      if (nodes.indexOf(node) === 0) {\n        ensureLastUndefined = true;\n      }\n    } else {\n      // bailed, we can't turn this statement into an expression\n      return;\n    }\n  }\n\n  if (ensureLastUndefined) {\n    exprs.push(buildUndefinedNode());\n  }\n\n  if (exprs.length === 1) {\n    return exprs[0];\n  } else {\n    return sequenceExpression(exprs);\n  }\n}\n"], "mappings": ";;;;;;AAOA,IAAAA,sBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAQA,IAAAE,OAAA,GAAAF,OAAA;AAKA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAA8C;AAQ/B,SAASK,yBAAyBA,CAC/CC,KAA4B,EAC5BC,OAA+B,EAC/B;EACA,MAAMC,KAAqB,GAAG,EAAE;EAChC,IAAIC,mBAAmB,GAAG,IAAI;EAE9B,KAAK,MAAMC,IAAI,IAAIJ,KAAK,EAAE;IAGxB,IAAI,CAAC,IAAAK,uBAAgB,EAACD,IAAI,CAAC,EAAE;MAC3BD,mBAAmB,GAAG,KAAK;IAC7B;IAEA,IAAI,IAAAG,mBAAY,EAACF,IAAI,CAAC,EAAE;MACtBF,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;IAClB,CAAC,MAAM,IAAI,IAAAI,4BAAqB,EAACJ,IAAI,CAAC,EAAE;MACtCF,KAAK,CAACK,IAAI,CAACH,IAAI,CAACK,UAAU,CAAC;IAC7B,CAAC,MAAM,IAAI,IAAAC,4BAAqB,EAACN,IAAI,CAAC,EAAE;MACtC,IAAIA,IAAI,CAACO,IAAI,KAAK,KAAK,EAAE;MAEzB,KAAK,MAAMC,MAAM,IAAIR,IAAI,CAACS,YAAY,EAAE;QACtC,MAAMC,QAAQ,GAAG,IAAAC,8BAAqB,EAACH,MAAM,CAAC;QAC9C,KAAK,MAAMI,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,EAAE;UACvCb,OAAO,CAACM,IAAI,CAAC;YACXI,IAAI,EAAEP,IAAI,CAACO,IAAI;YACfQ,EAAE,EAAE,IAAAC,kBAAS,EAACN,QAAQ,CAACE,GAAG,CAAC;UAC7B,CAAC,CAAC;QACJ;QAEA,IAAIJ,MAAM,CAACS,IAAI,EAAE;UACfnB,KAAK,CAACK,IAAI,CAAC,IAAAe,4BAAoB,EAAC,GAAG,EAAEV,MAAM,CAACO,EAAE,EAAEP,MAAM,CAACS,IAAI,CAAC,CAAC;QAC/D;MACF;MAEAlB,mBAAmB,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAI,IAAAoB,oBAAa,EAACnB,IAAI,CAAC,EAAE;MAC9B,MAAMoB,UAAU,GAAGpB,IAAI,CAACoB,UAAU,GAC9BzB,yBAAyB,CAAC,CAACK,IAAI,CAACoB,UAAU,CAAC,EAAEvB,OAAO,CAAC,GACrD,IAAAwB,+BAAkB,EAAC,CAAC;MACxB,MAAMC,SAAS,GAAGtB,IAAI,CAACsB,SAAS,GAC5B3B,yBAAyB,CAAC,CAACK,IAAI,CAACsB,SAAS,CAAC,EAAEzB,OAAO,CAAC,GACpD,IAAAwB,+BAAkB,EAAC,CAAC;MACxB,IAAI,CAACD,UAAU,IAAI,CAACE,SAAS,EAAE;MAE/BxB,KAAK,CAACK,IAAI,CAAC,IAAAoB,6BAAqB,EAACvB,IAAI,CAACwB,IAAI,EAAEJ,UAAU,EAAEE,SAAS,CAAC,CAAC;IACrE,CAAC,MAAM,IAAI,IAAAG,uBAAgB,EAACzB,IAAI,CAAC,EAAE;MACjC,MAAM0B,IAAI,GAAG/B,yBAAyB,CAACK,IAAI,CAAC0B,IAAI,EAAE7B,OAAO,CAAC;MAC1D,IAAI,CAAC6B,IAAI,EAAE;MAEX5B,KAAK,CAACK,IAAI,CAACuB,IAAI,CAAC;IAClB,CAAC,MAAM,IAAI,IAAAzB,uBAAgB,EAACD,IAAI,CAAC,EAAE;MAGjC,IAAIJ,KAAK,CAAC+B,OAAO,CAAC3B,IAAI,CAAC,KAAK,CAAC,EAAE;QAC7BD,mBAAmB,GAAG,IAAI;MAC5B;IACF,CAAC,MAAM;MAEL;IACF;EACF;EAEA,IAAIA,mBAAmB,EAAE;IACvBD,KAAK,CAACK,IAAI,CAAC,IAAAkB,+BAAkB,EAAC,CAAC,CAAC;EAClC;EAEA,IAAIvB,KAAK,CAAC8B,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO9B,KAAK,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACL,OAAO,IAAA+B,0BAAkB,EAAC/B,KAAK,CAAC;EAClC;AACF", "ignoreList": []}